import {
  Controller,
  Post,
  Body,
  ValidationPipe,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { RegisterUserDto } from './dto/register-user.dto';
import { LoginUserDto } from './dto/login-user.dto';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('/register')
  register(@Body(ValidationPipe) payload: RegisterUserDto) {
    return this.authService.register(payload);
  }

  @Post('/login')
  login(@Body(ValidationPipe) payload: LoginUserDto) {
    return this.authService.login(payload);
  }
}