import { Modu<PERSON> } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './users/users.module';
import { GeneratorController } from './generator/generator.controller';
import { GeneratorService } from './generator/generator.service';
import { GeneratorModule } from './generator/generator.module';
import { PrismaService } from './prisma/prisma.service';

@Module({
  imports: [UsersModule, GeneratorModule],
  controllers: [AppController, GeneratorController],
  providers: [AppService, GeneratorService, PrismaService],
})
export class AppModule {}
