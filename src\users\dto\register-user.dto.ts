import { IsEmail, IsString, Length, MinLength } from 'class-validator';
import { Match } from 'src/common/decorators/match.decorator';

export class RegisterUserDto {
  @IsString()
  @Length(5, 20, { message: 'Username must be between 5 and 20 characters' })
  username: string;

  @IsEmail({}, { message: 'Email must be valid' })
  email: string;

  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters' })
  password: string;

  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters' })
  @Match('password', { message: 'Passwords must match' })
  confirmPassword: string;
}

export type CreateUserInput = Omit<RegisterUserDto, 'confirmPassword'>;
