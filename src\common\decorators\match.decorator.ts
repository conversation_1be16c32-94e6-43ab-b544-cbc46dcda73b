import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
} from 'class-validator';

export function Match(property: string, validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'Match',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [property],
      validator: {
        validate(value: unknown, args: ValidationArguments): boolean {
          const [relatedPropertyName] = args.constraints as [string];

          // Fully typed and guarded access to the other property
          if (
            typeof args.object === 'object' &&
            args.object !== null &&
            relatedPropertyName in args.object
          ) {
            const relatedValue = (args.object as Record<string, unknown>)[
              relatedPropertyName
            ];
            return value === relatedValue;
          }

          return false;
        },

        defaultMessage(args: ValidationArguments): string {
          return `${args.property} must match ${args.constraints[0]}`;
        },
      },
    });
  };
}
