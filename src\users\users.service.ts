import { Injectable } from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateUserInput, RegisterUserDto } from './dto/register-user.dto';
import { UserRepository } from './user.repository';

@Injectable()
export class UsersService {
  constructor(private userRepo: UserRepository) {}

  create(createUserDto: CreateUserDto) {
    return 'This action adds a new user';
  }

  findAll() {
    return this.userRepo.getAll();
  }

  findOne(id: number) {
    return `This action returns a #${id} user`;
  }

  update(id: number, updateUserDto: UpdateUserDto) {
    return `This action updates a #${id} user`;
  }

  remove(id: number) {
    return `This action removes a #${id} user`;
  }

  login(payload) {
    return 'This action logs in a user';
  }

  async register(payload: RegisterUserDto) {
    const userData: CreateUserInput = {
      email: payload.email,
      username: payload.username,
      password: payload.password,
    };

    try {
      return this.userRepo.create(userData);
    } catch (error) {
      console.error(error);
    }
  }
}
