import { Injectable } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { RegisterUserDto } from './dto/register-user.dto';
import { LoginUserDto } from './dto/login-user.dto';

@Injectable()
export class AuthService {
  constructor(private usersService: UsersService) {}

  async register(payload: RegisterUserDto) {
    const userData = {
      email: payload.email,
      username: payload.username,
      password: payload.password,
    };

    try {
      return this.usersService.create(userData);
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async login(payload: LoginUserDto) {
    // TODO: Implement login logic
    return 'This action logs in a user';
  }
}